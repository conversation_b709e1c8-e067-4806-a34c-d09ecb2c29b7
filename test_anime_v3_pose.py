#!/usr/bin/env python3
"""
Test script for Anime V3 Pose Generation functionality
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly"""
    try:
        from comfy.defines import workflow_map, AnimeV3PoseWorkflow
        from comfy.apis import AnimeV3PoseWorkflow as AnimeV3PoseWorkflowAPI
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_workflow_mapping():
    """Test that the workflow mapping is correctly configured"""
    try:
        from comfy.defines import workflow_map
        
        if 'anime_v3_pose' in workflow_map:
            mapping = workflow_map['anime_v3_pose']
            print(f"✅ Workflow mapping found: {mapping}")
            
            # Check required fields
            required_fields = ['obj', 'model', 'workflow_file', 'desc']
            for field in required_fields:
                if field not in mapping:
                    print(f"❌ Missing required field: {field}")
                    return False
            
            # Check workflow file
            if mapping['workflow_file'] != 'anime_v3_pose.json':
                print(f"❌ Wrong workflow file: {mapping['workflow_file']}")
                return False
                
            print("✅ Workflow mapping is correctly configured")
            return True
        else:
            print("❌ anime_v3_pose not found in workflow_map")
            return False
    except Exception as e:
        print(f"❌ Error testing workflow mapping: {e}")
        return False

def test_workflow_class():
    """Test that the AnimeV3PoseWorkflow class is properly implemented"""
    try:
        from comfy.apis import AnimeV3PoseWorkflow
        
        # Test class instantiation
        workflow = AnimeV3PoseWorkflow(
            server_address="localhost:8188",
            workflow_file="anime_v3_pose.json",
            extra_params={
                'prompt': 'test prompt',
                'model_id': 'test_model',
                'seed': 12345
            }
        )
        
        print("✅ AnimeV3PoseWorkflow class instantiated successfully")
        
        # Test that it has the required methods
        if hasattr(workflow, 'get_prompt'):
            print("✅ get_prompt method exists")
        else:
            print("❌ get_prompt method missing")
            return False
            
        if hasattr(workflow, 'run'):
            print("✅ run method exists")
        else:
            print("❌ run method missing")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Error testing workflow class: {e}")
        return False

def test_workflow_file_exists():
    """Test that the anime_v3_pose.json workflow file exists"""
    try:
        workflow_path = os.path.join('comfy', 'workflows', 'anime_v3_pose.json')
        if os.path.exists(workflow_path):
            print(f"✅ Workflow file exists: {workflow_path}")
            return True
        else:
            print(f"❌ Workflow file not found: {workflow_path}")
            return False
    except Exception as e:
        print(f"❌ Error checking workflow file: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Anime V3 Pose Generation Implementation")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Workflow File Test", test_workflow_file_exists),
        ("Workflow Mapping Test", test_workflow_mapping),
        ("Workflow Class Test", test_workflow_class),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Anime V3 Pose Generation is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
