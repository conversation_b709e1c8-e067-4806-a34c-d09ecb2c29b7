#!/usr/bin/env python3
"""
测试 openposeControl 与动漫 V3 模型的集成
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_workflow_selection():
    """测试工作流选择逻辑"""
    try:
        from utils.task_utils import get_workflow_obj
        from comfy.defines import model_info_dict
        
        print("🧪 测试工作流选择逻辑")
        print("=" * 50)
        
        # 测试动漫V3模型的openposeControl
        anime_v3_task = {
            'comfy_server': 'localhost:8188',
            'params': {
                'model_id': 'anime_v3_test',  # 假设这是一个动漫V3模型ID
                'img2img_info': {
                    'style': 'openposeControl',
                    'img_url': 'test_image.jpg'
                },
                'prompt': '1girl, beautiful, anime style'
            },
            'task_type': 'img2img',
            'prompt_id': 'test_prompt_id',
            'client_id': 'test_client_id'
        }
        
        # 测试普通模型的openposeControl
        normal_task = {
            'comfy_server': 'localhost:8188',
            'params': {
                'model_id': 'normal_model_test',  # 假设这是一个普通模型ID
                'img2img_info': {
                    'style': 'openposeControl',
                    'img_url': 'test_image.jpg'
                },
                'prompt': '1girl, beautiful'
            },
            'task_type': 'img2img',
            'prompt_id': 'test_prompt_id',
            'client_id': 'test_client_id'
        }
        
        print("✅ 测试任务创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_anime_v3_detection():
    """测试动漫V3模型检测逻辑"""
    try:
        print("\n🔍 测试动漫V3模型检测")
        print("-" * 30)
        
        # 模拟检测逻辑
        test_cases = [
            ('anime_v3_model', True),
            ('ANIME_V3_TEST', True),
            ('normal_model', False),
            ('realistic_v2', False),
        ]
        
        for model_id, expected in test_cases:
            # 模拟检测逻辑
            is_anime_v3 = 'anime_v3' in str(model_id).lower()
            
            if is_anime_v3 == expected:
                print(f"✅ {model_id}: {is_anime_v3} (预期: {expected})")
            else:
                print(f"❌ {model_id}: {is_anime_v3} (预期: {expected})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检测逻辑测试失败: {e}")
        return False

def test_workflow_class_methods():
    """测试工作流类方法"""
    try:
        from comfy.apis import Img2ImgGenImageWorkflow
        
        print("\n🔧 测试工作流类方法")
        print("-" * 30)
        
        # 创建工作流实例
        workflow = Img2ImgGenImageWorkflow(
            server_address="localhost:8188",
            workflow_file="i2i_openpose_gen.json",
            extra_params={
                'model_id': 'anime_v3_test',
                'prompt': 'test prompt',
                'img2img_info': {
                    'style': 'openposeControl',
                    'weight': 0.8
                }
            }
        )
        
        # 检查方法是否存在
        methods_to_check = [
            'get_i2i_openposeControl_prompt',
            '_get_anime_v3_openpose_prompt',
            '_get_default_openpose_prompt'
        ]
        
        for method_name in methods_to_check:
            if hasattr(workflow, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流类测试失败: {e}")
        return False

def test_workflow_files():
    """测试工作流文件是否存在"""
    try:
        print("\n📁 测试工作流文件")
        print("-" * 30)
        
        workflow_files = [
            'comfy/workflows/i2i_openpose_gen.json',
            'comfy/workflows/anime_v3_pose.json'
        ]
        
        for file_path in workflow_files:
            if os.path.exists(file_path):
                print(f"✅ 工作流文件存在: {file_path}")
            else:
                print(f"❌ 工作流文件不存在: {file_path}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件检查失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🧪 测试 openposeControl 与动漫 V3 模型集成")
    print("=" * 60)
    
    tests = [
        ("工作流文件测试", test_workflow_files),
        ("动漫V3检测测试", test_anime_v3_detection),
        ("工作流类方法测试", test_workflow_class_methods),
        ("工作流选择测试", test_workflow_selection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出现异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！openposeControl 与动漫 V3 模型集成成功。")
        print("\n📝 使用说明:")
        print("- 当 model_id 包含 'anime_v3' 时，会自动使用 anime_v3_pose.json 工作流")
        print("- 其他模型继续使用原来的 i2i_openpose_gen.json 工作流")
        print("- style 参数仍然使用 'openposeControl'，无需修改前端代码")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
