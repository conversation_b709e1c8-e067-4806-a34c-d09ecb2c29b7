{"1": {"inputs": {"image": "image (23).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "3": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["11", 0], "height": ["11", 1], "crop": "disabled", "image": ["1", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "4": {"inputs": {"strength": 0.8, "start_percent": 0, "end_percent": 1, "positive": ["7", 0], "negative": ["10", 0], "control_net": ["5", 0], "image": ["3", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "5": {"inputs": {"type": "openpose", "control_net": ["6", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "6": {"inputs": {"control_net_name": "ContolnetPlus_promax.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "7": {"inputs": {"text": "1girl, upper body, blone hair, long curl hair, jeans", "clip": ["17", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"ckpt_name": "animagineXL40_v4Opt.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "9": {"inputs": {"seed": 630283909787707, "steps": 25, "cfg": 7, "sampler_name": "euler_ancestral", "scheduler": "normal", "denoise": 1, "model": ["17", 0], "positive": ["4", 0], "negative": ["4", 1], "latent_image": ["12", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "10": {"inputs": {"text": "(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name", "clip": ["17", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "11": {"inputs": {"image": ["1", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "12": {"inputs": {"width": ["11", 0], "height": ["11", 1], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "13": {"inputs": {"samples": ["9", 0], "vae": ["8", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "14": {"inputs": {"filename_prefix": "ComfyUI", "images": ["13", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "16": {"inputs": {"lora_name": "LCMTurboMix_Euler_A_fix.safetensors", "strength_model": 0.3, "strength_clip": 1, "model": ["8", 0], "clip": ["8", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "17": {"inputs": {"lora_name": "cfg_scale_boost.safetensors", "strength_model": 0.15, "strength_clip": 1, "model": ["18", 0], "clip": ["18", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "18": {"inputs": {"lora_name": "ClearHand-V2.safetensors", "strength_model": 0.8, "strength_clip": 1, "model": ["16", 0], "clip": ["16", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}}